'use client';

import React, { useState } from 'react'; // Removed useEffect, use<PERSON><PERSON>back as <PERSON>wi<PERSON> handles autoplay
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils/cn';
import type { DiscoverBannerItem } from '@/lib/api/types';

// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperClass } from 'swiper/types'; // For Swiper instance type

// Import Swiper modules
import { Autoplay, EffectFade } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/effect-fade';

// 模拟 API 返回的 Banner 数据
const mockBanners: DiscoverBannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/hfZ6QtNo0puiIz8AUGys.png',
    title: 'Toxic Romance: Can you handle their extreme love?',
    linkUrl: '/discover/detail/1',
    altText: 'Banner image 1 featuring characters from Toxic Romance',
  },
  {
    id: '2',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/C2xWu9skzYxfb8UNIam8.png',
    title: 'New Adventure Awaits!',
    linkUrl: '/discover/detail/2',
    altText: 'Banner image 2 announcing a new adventure',
  },
  {
    id: '3',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/BnNgHTSIo7eiktokIvPD.png',
    title: 'Discover Hidden Gems',
    linkUrl: '/discover/detail/3',
    altText: 'Banner image 3 showcasing hidden gems',
  },
  {
    id: '4',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/j9KZaTQoUCsriO6Qr6Lq.png',
    title: 'Limited Time Offer!',
    linkUrl: '/discover/detail/4',
    altText: 'Banner image 4 for a limited time offer',
  },
];

interface BannerCarouselProps {
  banners?: DiscoverBannerItem[];
  autoplayInterval?: number; // 自动播放间隔，单位毫秒
}

const BannerCarousel: React.FC<BannerCarouselProps> = ({
  banners = mockBanners,
  autoplayInterval = 5000,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [swiperInstance, setSwiperInstance] = useState<SwiperClass | null>(null);
  const router = useRouter();

  const handleDotClick = (index: number) => {
    if (swiperInstance) {
      swiperInstance.slideToLoop(index); 
    }
  };

  const handleBannerClick = (banner: DiscoverBannerItem) => {
    if (banner.linkUrl) {
      router.push(banner.linkUrl);
    }
  };

  if (!banners || banners.length === 0) {
    return (
      <div className="w-full aspect-[16/10] bg-gray-200 rounded-xl flex items-center justify-center">
        <p className="text-gray-500">No banners to display.</p>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-center"> {/* Overall component wrapper */}
      {/* Banner Image Area with fixed aspect ratio */}
      <div
        className="w-full aspect-[16/10] overflow-hidden rounded-xl shadow-lg relative"
        data-testid="banner-carousel-root"
      >
        <Swiper
          modules={[Autoplay, EffectFade]}
          effect="fade"
          fadeEffect={{ crossFade: true }}
          autoplay={ banners.length > 1 ? { delay: autoplayInterval, disableOnInteraction: false } : false }
          loop={banners.length > 1} // Only loop if more than one banner
          slidesPerView={1}
          grabCursor={true}
          onSwiper={setSwiperInstance}
          onSlideChange={(swiper) => setCurrentIndex(swiper.realIndex)}
          className="w-full h-full" // Ensure Swiper container fills the aspect ratio container
        >
          {banners.map((banner, index) => (
            <SwiperSlide
              key={banner.id}
              onClick={() => handleBannerClick(banner)}
              className="cursor-pointer"
            >
              <div className="relative w-full h-full">
                <Image
                  src={banner.imageUrl}
                  alt={banner.altText || `Banner ${index + 1}`}
                  fill={true}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw"
                  priority={index === 0}
                  className="object-cover" // This ensures the image scales to fit while maintaining aspect ratio
                />
                {/* Gradient overlay */}
                <div className="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom Navigation Dots Area - Circular dots */}
        {banners.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center justify-center gap-2.5 z-20">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to slide ${index + 1}`}
                className={cn(
                  'w-[5px] h-[5px] rounded-full transition-all duration-300 ease-in-out',
                  currentIndex === index
                    ? 'bg-white' // Active dot style - solid white
                    : 'bg-white/30 hover:bg-white/60' // Inactive dot style - semi-transparent white
                )}
              />
            ))}
          </div>
        )}
      </div> {/* End of Banner Image Area */}
    </div>
  );
};

export default BannerCarousel;